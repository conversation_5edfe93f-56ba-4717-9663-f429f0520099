const { body, param, query, validationResult } = require('express-validator');

// Common validation rules
const emailValidation = body('email')
    .isEmail()
    .normalizeEmail()
    .withMessage('Please provide a valid email address');

const passwordValidation = body('password')
    .isLength({ min: 8 })
    .withMessage('Password must be at least 8 characters long')
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/)
    .withMessage('Password must contain at least one lowercase letter, one uppercase letter, and one number');

const nameValidation = (field) => body(field)
    .trim()
    .isLength({ min: 1, max: 50 })
    .withMessage(`${field} is required and must be between 1 and 50 characters`);

const phoneValidation = body('phone')
    .optional()
    .isMobilePhone()
    .withMessage('Please provide a valid phone number');

const uuidValidation = (field) => param(field)
    .isUUID()
    .withMessage(`${field} must be a valid UUID`);

// Validation middleware
const handleValidationErrors = (req, res, next) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
        return res.status(400).json({
            success: false,
            message: 'Validation failed',
            errors: errors.array()
        });
    }
    next();
};

// Specific validation rules for different endpoints
const registerValidation = [
    emailValidation,
    passwordValidation,
    nameValidation('firstName'),
    nameValidation('lastName'),
    phoneValidation,
    body('newsletter')
        .optional()
        .isBoolean()
        .withMessage('Newsletter subscription must be a boolean'),
    handleValidationErrors
];

const loginValidation = [
    emailValidation,
    body('password').exists().withMessage('Password is required'),
    handleValidationErrors
];

const menuItemValidation = [
    body('name')
        .trim()
        .isLength({ min: 1, max: 100 })
        .withMessage('Name is required and must be between 1 and 100 characters'),
    body('description')
        .optional()
        .trim()
        .isLength({ max: 500 })
        .withMessage('Description must be less than 500 characters'),
    body('price')
        .isFloat({ min: 0 })
        .withMessage('Price must be a positive number'),
    body('category')
        .trim()
        .isLength({ min: 1, max: 50 })
        .withMessage('Category is required and must be between 1 and 50 characters'),
    body('isAvailable')
        .optional()
        .isBoolean()
        .withMessage('isAvailable must be a boolean'),
    handleValidationErrors
];

const orderValidation = [
    body('items')
        .isArray({ min: 1 })
        .withMessage('Order must contain at least one item'),
    body('items.*.menuItemId')
        .isUUID()
        .withMessage('Each item must have a valid menu item ID'),
    body('items.*.quantity')
        .isInt({ min: 1 })
        .withMessage('Each item must have a quantity of at least 1'),
    body('orderType')
        .isIn(['dine_in', 'takeaway', 'delivery'])
        .withMessage('Order type must be dine_in, takeaway, or delivery'),
    body('customerInfo')
        .isObject()
        .withMessage('Customer information is required'),
    body('customerInfo.firstName')
        .trim()
        .isLength({ min: 1 })
        .withMessage('Customer first name is required'),
    body('customerInfo.lastName')
        .trim()
        .isLength({ min: 1 })
        .withMessage('Customer last name is required'),
    body('customerInfo.phone')
        .isMobilePhone()
        .withMessage('Valid customer phone number is required'),
    handleValidationErrors
];

module.exports = {
    registerValidation,
    loginValidation,
    menuItemValidation,
    orderValidation,
    uuidValidation,
    handleValidationErrors
};
