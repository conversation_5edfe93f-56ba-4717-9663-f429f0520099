const sequelize = require('../config/database');
const User = require('./User');
const MenuItem = require('./MenuItem');
const Order = require('./Order');

// Define associations
User.hasMany(Order, {
    foreignKey: 'userId',
    as: 'orders'
});

Order.belongsTo(User, {
    foreignKey: 'userId',
    as: 'user'
});

// Export models and sequelize instance
module.exports = {
    sequelize,
    User,
    MenuItem,
    Order
};
