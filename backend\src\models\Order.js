const { DataTypes } = require('sequelize');
const sequelize = require('../config/database');

const Order = sequelize.define('Order', {
    id: {
        type: DataTypes.UUID,
        defaultValue: DataTypes.UUIDV4,
        primaryKey: true
    },
    userId: {
        type: DataTypes.UUID,
        allowNull: false,
        field: 'user_id',
        references: {
            model: 'users',
            key: 'id'
        }
    },
    orderNumber: {
        type: DataTypes.STRING,
        allowNull: false,
        unique: true,
        field: 'order_number'
    },
    status: {
        type: DataTypes.ENUM(
            'pending',
            'confirmed',
            'preparing',
            'ready',
            'delivered',
            'cancelled'
        ),
        defaultValue: 'pending'
    },
    totalAmount: {
        type: DataTypes.DECIMAL(10, 2),
        allowNull: false,
        field: 'total_amount',
        validate: {
            min: 0
        }
    },
    subtotal: {
        type: DataTypes.DECIMAL(10, 2),
        allowNull: false,
        validate: {
            min: 0
        }
    },
    taxAmount: {
        type: DataTypes.DECIMAL(10, 2),
        allowNull: false,
        field: 'tax_amount',
        defaultValue: 0,
        validate: {
            min: 0
        }
    },
    deliveryFee: {
        type: DataTypes.DECIMAL(10, 2),
        allowNull: false,
        field: 'delivery_fee',
        defaultValue: 0,
        validate: {
            min: 0
        }
    },
    paymentStatus: {
        type: DataTypes.ENUM('pending', 'paid', 'failed', 'refunded'),
        defaultValue: 'pending',
        field: 'payment_status'
    },
    paymentMethod: {
        type: DataTypes.STRING,
        allowNull: true,
        field: 'payment_method'
    },
    paymentReference: {
        type: DataTypes.STRING,
        allowNull: true,
        field: 'payment_reference'
    },
    orderType: {
        type: DataTypes.ENUM('dine_in', 'takeaway', 'delivery'),
        allowNull: false,
        field: 'order_type'
    },
    customerInfo: {
        type: DataTypes.JSONB,
        allowNull: false,
        field: 'customer_info'
    },
    deliveryAddress: {
        type: DataTypes.JSONB,
        allowNull: true,
        field: 'delivery_address'
    },
    specialInstructions: {
        type: DataTypes.TEXT,
        allowNull: true,
        field: 'special_instructions'
    },
    estimatedDeliveryTime: {
        type: DataTypes.DATE,
        allowNull: true,
        field: 'estimated_delivery_time'
    },
    actualDeliveryTime: {
        type: DataTypes.DATE,
        allowNull: true,
        field: 'actual_delivery_time'
    },
    items: {
        type: DataTypes.JSONB,
        allowNull: false,
        defaultValue: []
    }
}, {
    tableName: 'orders',
    underscored: true,
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    indexes: [
        {
            fields: ['user_id']
        },
        {
            fields: ['status']
        },
        {
            fields: ['payment_status']
        },
        {
            fields: ['order_type']
        },
        {
            fields: ['created_at']
        }
    ]
});

module.exports = Order;
