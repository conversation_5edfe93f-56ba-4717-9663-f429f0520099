const { DataTypes } = require('sequelize');
const sequelize = require('../config/database');
const bcrypt = require('bcryptjs');

const User = sequelize.define('User', {
    id: {
        type: DataTypes.UUID,
        defaultValue: DataTypes.UUIDV4,
        primaryKey: true
    },
    email: {
        type: DataTypes.STRING,
        allowNull: false,
        unique: true,
        validate: {
            isEmail: true
        }
    },
    passwordHash: {
        type: DataTypes.STRING,
        allowNull: false,
        field: 'password_hash'
    },
    firstName: {
        type: DataTypes.STRING,
        allowNull: false,
        field: 'first_name'
    },
    lastName: {
        type: DataTypes.STRING,
        allowNull: false,
        field: 'last_name'
    },
    phone: {
        type: DataTypes.STRING,
        allowNull: true
    },
    emailVerified: {
        type: DataTypes.BOOLEAN,
        defaultValue: false,
        field: 'email_verified'
    },
    verificationToken: {
        type: DataTypes.STRING,
        allowNull: true,
        field: 'verification_token'
    },
    verificationExpiresAt: {
        type: DataTypes.DATE,
        allowNull: true,
        field: 'verification_expires_at'
    },
    newsletterSubscribed: {
        type: DataTypes.BOOLEAN,
        defaultValue: false,
        field: 'newsletter_subscribed'
    },
    lastLoginAt: {
        type: DataTypes.DATE,
        allowNull: true,
        field: 'last_login_at'
    },
    isActive: {
        type: DataTypes.BOOLEAN,
        defaultValue: true,
        field: 'is_active'
    }
}, {
    tableName: 'users',
    underscored: true,
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at'
});

// Instance methods
User.prototype.validatePassword = async function(password) {
    return bcrypt.compare(password, this.passwordHash);
};

User.prototype.toJSON = function() {
    const values = { ...this.get() };
    delete values.passwordHash;
    delete values.verificationToken;
    return values;
};

// Class methods
User.hashPassword = async function(password) {
    return bcrypt.hash(password, 12);
};

module.exports = User;
