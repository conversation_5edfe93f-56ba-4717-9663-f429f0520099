const { DataTypes } = require('sequelize');
const sequelize = require('../config/database');

const MenuItem = sequelize.define('MenuItem', {
    id: {
        type: DataTypes.UUID,
        defaultValue: DataTypes.UUIDV4,
        primaryKey: true
    },
    name: {
        type: DataTypes.STRING,
        allowNull: false
    },
    description: {
        type: DataTypes.TEXT,
        allowNull: true
    },
    price: {
        type: DataTypes.DECIMAL(10, 2),
        allowNull: false,
        validate: {
            min: 0
        }
    },
    category: {
        type: DataTypes.STRING,
        allowNull: false
    },
    imageUrl: {
        type: DataTypes.STRING,
        allowNull: true,
        field: 'image_url'
    },
    isAvailable: {
        type: DataTypes.BOOLEAN,
        defaultValue: true,
        field: 'is_available'
    },
    preparationTime: {
        type: DataTypes.INTEGER,
        allowNull: true,
        field: 'preparation_time',
        comment: 'Preparation time in minutes'
    },
    ingredients: {
        type: DataTypes.ARRAY(DataTypes.STRING),
        allowNull: true,
        defaultValue: []
    },
    allergens: {
        type: DataTypes.ARRAY(DataTypes.STRING),
        allowNull: true,
        defaultValue: []
    },
    nutritionalInfo: {
        type: DataTypes.JSONB,
        allowNull: true,
        field: 'nutritional_info'
    },
    isVegetarian: {
        type: DataTypes.BOOLEAN,
        defaultValue: false,
        field: 'is_vegetarian'
    },
    isVegan: {
        type: DataTypes.BOOLEAN,
        defaultValue: false,
        field: 'is_vegan'
    },
    isGlutenFree: {
        type: DataTypes.BOOLEAN,
        defaultValue: false,
        field: 'is_gluten_free'
    },
    spiceLevel: {
        type: DataTypes.INTEGER,
        allowNull: true,
        field: 'spice_level',
        validate: {
            min: 0,
            max: 5
        }
    },
    sortOrder: {
        type: DataTypes.INTEGER,
        defaultValue: 0,
        field: 'sort_order'
    }
}, {
    tableName: 'menu_items',
    underscored: true,
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    indexes: [
        {
            fields: ['category']
        },
        {
            fields: ['is_available']
        },
        {
            fields: ['sort_order']
        }
    ]
});

module.exports = MenuItem;
